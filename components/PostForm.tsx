import { zodResolver } from '@hookform/resolvers/zod';
import * as ImageManipulator from 'expo-image-manipulator';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import { ForwardedRef, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Alert, FlatList, Pressable, ScrollView, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import * as z from 'zod';

import { ICommentResponse } from '@/apis/comment/types';
import { IEpisode, IPodcast } from '@/apis/podcast';
import { ThemedText } from '@/components/ThemedText';
import TextInput from '@/components/ui/TextInput';
import { TextareaInput } from '@/components/ui/TextareaInput';
import { ZOD_ERRORS } from '@/config/zodError';
import { Ionicons } from '@expo/vector-icons';
import { Spacer } from './Spacer';
import { ExpoImage } from './ui/Image';
import { Icons } from '@/assets/icons';
import { toastError } from '@/utils/toast';

// Form schema with zod validation
const postSchema = z.object({
  title: z.string().trim().max(100, ZOD_ERRORS.postTitleLength),
  comment: z.string().trim().min(1, ZOD_ERRORS.required).max(500, ZOD_ERRORS.postContentLength),
  images: z.array(z.string()).max(5, ZOD_ERRORS.postImagesLength).optional(),
});

export type PostFormData = z.infer<typeof postSchema>;

export interface PostFormRef {
  submit: () => void;
}

interface PostFormProps {
  onSubmit: (data: PostFormData) => void;
  onFormSubmittingChange?: (isSubmitting: boolean) => void;
  onFormDirtyChange?: (isDirty: boolean) => void;
  initialData?: ICommentResponse;
  podcast?: IPodcast;
  episode?: IEpisode;
}

const PostForm = forwardRef(
  (
    { onSubmit, onFormSubmittingChange, onFormDirtyChange, initialData, podcast, episode }: PostFormProps,
    ref: ForwardedRef<PostFormRef>
  ) => {
    const [selectedImages, setSelectedImages] = useState<string[]>(initialData?.images || []);
    const [images, setImages] = useState<MediaLibrary.Asset[]>([]);
    const [hasPermission, setHasPermission] = useState<boolean>(false);

    const { styles } = useStyles(stylesheet);

    useEffect(() => {
      if (initialData?.images) {
        setSelectedImages(initialData.images);
      }
      setValue('images', initialData?.images || []);
      setValue('title', initialData?.title || '');
      setValue('comment', initialData?.content || '');
    }, [initialData]);

    const {
      control,
      handleSubmit,
      setValue,
      formState: { errors, isValid, isSubmitting, isDirty },
    } = useForm<PostFormData>({
      resolver: zodResolver(postSchema),
      defaultValues: {
        title: initialData?.title || '',
        comment: initialData?.content || '',
        images: initialData?.images,
      },
    });

    // Expose submit method via ref
    useImperativeHandle(ref, () => ({
      submit: handleSubmit((data) => onSubmit(data)),
    }));

    useEffect(() => {
      if (onFormSubmittingChange) {
        onFormSubmittingChange(isSubmitting);
      }
    }, [isSubmitting, onFormSubmittingChange]);

    useEffect(() => {
      if (onFormDirtyChange) {
        onFormDirtyChange(isDirty);
      }
    }, [isDirty, onFormDirtyChange]);

    useEffect(() => {
      (async () => {
        // Request media library permissions
        const { status } = await MediaLibrary.requestPermissionsAsync();
        setHasPermission(status === 'granted');

        if (status === 'granted') {
          loadImages();
        }
      })();
    }, []);

    const loadImages = async () => {
      const media = await MediaLibrary.getAssetsAsync({
        mediaType: 'photo',
        first: 4,
        sortBy: ['creationTime'],
      });
      setImages(media.assets);
    };

    const pickImage = async () => {
      if (selectedImages.length >= 5) return;

      try {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

        if (status !== 'granted') {
          Alert.alert('Permission required', 'Please grant permission to access your photos');
          return;
        }

        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ['images'],
          allowsMultipleSelection: true,
          quality: 0.5,
          selectionLimit: 5 - selectedImages.length,
        });

        const compressResult = await result.assets?.map(async (asset) => {
          const imageResult = await (
            await ImageManipulator.ImageManipulator.manipulate(asset.uri).renderAsync()
          ).saveAsync({
            compress: 0.5,
          });

          return imageResult;
        });

        console.log(
          1,
          result?.assets?.map((asset) => (asset.fileSize || 0) / 1024)
        );
        console.log(1, compressResult);

        if (!result.canceled && result.assets) {
          const newImages = result.assets.map((asset) => asset.uri);
          const updatedImages = [...selectedImages, ...newImages].slice(0, 6); // Limit to 6 images
          setSelectedImages(updatedImages);
          setValue('images', updatedImages);
        }
      } catch (error) {
        const hasErrorCode = (err: unknown): err is { code: string; message?: string } => {
          return typeof err === 'object' && err !== null && 'code' in err;
        };

        if (hasErrorCode(error)) {
          if (error.code === 'ERR_FAILED_CREATING_UI_IMAGE') {
            toastError('Some images are invalid. Please try again');
          }
        }
      }
    };

    const removeImage = (index: number) => {
      const updatedImages = selectedImages.filter((_, i) => i !== index);
      setSelectedImages(updatedImages);
      setValue('images', updatedImages);
    };

    const renderImageItem = ({ item, index }: { item: MediaLibrary.Asset; index: number }) => (
      <Pressable onPress={pickImage} style={styles.imageContainer}>
        <ExpoImage source={{ uri: item.uri }} style={styles.selectedImage} />
      </Pressable>
    );

    const renderSelectedImageItem = ({ item, index }: { item: string; index: number }) => (
      <Pressable onPress={pickImage} style={[styles.imageContainer, { width: 180, height: 180 }]}>
        <ExpoImage source={{ uri: item }} style={[styles.selectedImage, { width: 180, height: 180 }]} />

        <Ionicons
          name='close-outline'
          size={28}
          color='black'
          style={styles.removeButton}
          onPress={() => removeImage(index)}
        />
      </Pressable>
    );

    return (
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false} bounces={false}>
        {/* Show Preview Card */}
        <View style={styles.showPreviewCard}>
          <View style={styles.showImageContainer}>
            <ExpoImage source={{ uri: podcast?.imageUrl || episode?.imageUrl }} style={styles.showImage} />
          </View>

          <View style={styles.showDetailsContainer}>
            <ThemedText type='subtitleSemiBold' style={styles.showTitle}>
              {podcast?.title || episode?.title}
            </ThemedText>
          </View>
        </View>

        <Controller
          control={control}
          name='title'
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              placeholder='Title (Optional)'
              placeholderTextColor='#666'
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.title?.message}
              editable={!isSubmitting}
            />
          )}
        />

        <Spacer height={16} />

        <Controller
          control={control}
          name='comment'
          render={({ field: { onChange, onBlur, value } }) => (
            <TextareaInput
              placeholder='Write your thoughts about this show'
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.comment?.message}
              editable={!isSubmitting}
            />
          )}
        />

        <View style={styles.imageSection}>
          {selectedImages.length > 0 && (
            <FlatList
              data={selectedImages}
              renderItem={renderSelectedImageItem}
              keyExtractor={(item) => item.toString()}
              horizontal={true}
              showsHorizontalScrollIndicator={false}
            />
          )}
          <FlatList
            data={images}
            renderItem={renderImageItem}
            keyExtractor={(item, index) => index.toString()}
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            ListHeaderComponent={
              <TouchableOpacity style={styles.addImageButton} onPress={pickImage} disabled={selectedImages.length >= 5}>
                <Icons.Image style={styles.addImageIcon} size={32} color='#D9FF03' />
              </TouchableOpacity>
            }
          />
        </View>
      </ScrollView>
    );
  }
);

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    paddingTop: 20,
  },
  showPreviewCard: {
    flexDirection: 'row',
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'center',
  },
  showImageContainer: {
    position: 'relative',
  },
  showImage: {
    width: 64,
    height: 64,
    borderRadius: 8,
  },
  showDetailsContainer: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'center',
  },
  showTitle: {
    fontSize: 16,
    color: theme.colors.neutralWhite,
  },
  imageSection: {
    flex: 1,
    marginTop: 20,
    flexDirection: 'column',
    justifyContent: 'space-between',
    gap: 20,
  },
  addImageButton: {
    width: 72,
    height: 72,
    borderRadius: 16,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.colors.primary,
    alignItems: 'center',
    marginRight: 12,
  },
  addImageIcon: {
    color: theme.colors.primary,
    width: 24,
    height: 24,
  },
  imageContainer: {
    marginRight: 12,
    position: 'relative',
  },
  selectedImage: {
    width: 72,
    height: 72,
    borderRadius: 16,
  },
  removeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    borderRadius: 999,
    backgroundColor: theme.colors.neutralWhite,
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
}));

export default PostForm;
