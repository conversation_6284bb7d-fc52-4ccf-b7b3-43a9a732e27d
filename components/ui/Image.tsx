import { useRecyclingState } from '@shopify/flash-list';
import { Image, ImageProps } from 'expo-image';
import Animated from 'react-native-reanimated';

type TSource = ImageProps['source'];
type Props = {} & ImageProps;

const blurhash =
  '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

export const ExpoImage = (props: Props) => {
  const [source, setSource] = useRecyclingState<TSource>(props.source, [props.source]);

  const handleLoadError = (error: any) => {
    setSource(require('@/assets/images/empty_cover.png'));
  };

  const recyclingKey =
    typeof props.source === 'object' && !Array.isArray(props.source) && props.source && 'uri' in props.source
      ? props.source.uri
      : undefined;

  return (
    <Image
      {...props}
      transition={100}
      cachePolicy='memory-disk'
      onError={handleLoadError}
      placeholder={{ blurhash }}
      recyclingKey={recyclingKey}
      placeholderContentFit='cover'
      source={source}
    />
  );
};

export const ExpoImageAnimated = Animated.createAnimatedComponent(ExpoImage);
